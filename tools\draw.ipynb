import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import root_mean_squared_error, r2_score

plt.style.use('ggplot') # 应用主题
# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman']

# 读取数据
df = pd.read_csv(r"E:\tobacco\tobacco_det_data\batch2_384\approach\mine_kmeans\results.csv")

# 提取数据
reference_tobaccos = df["n_ground_truth"]
detected_tp = df["n_matched"]
detected_tp_fp = df["n_inference"]

# 回归与误差计算
r2_tp_fp = r2_score(reference_tobaccos, detected_tp_fp)
rmse_tp_fp = root_mean_squared_error(reference_tobaccos, detected_tp_fp)

r2_tp = r2_score(reference_tobaccos, detected_tp)
rmse_tp = root_mean_squared_error(reference_tobaccos, detected_tp)

# 分类指标平均值
mean_precision = df["precision"].mean()
mean_recall = df["recall"].mean()
mean_f1 = df["f1_score"].mean()


# 图形设置
fig, ax = plt.subplots(figsize=(6, 6))
ax.set_facecolor("#eaeaf2")

# 计算坐标轴范围并截断左侧空白
min_ref = reference_tobaccos.min()
max_ref = reference_tobaccos.max()
min_det = min(detected_tp.min(), detected_tp_fp.min())
max_det = max(detected_tp.max(), detected_tp_fp.max())

axis_min = min(min_ref, min_det) - 50
axis_max = max(max_ref, max_det) + 50

axis_min = 200
axis_max = 1300

# 绘制对角虚线 y=x，确保在背景层可见
ax.plot([axis_min, axis_max], [axis_min, axis_max], "k--", alpha=0.5, zorder=0)

# 散点图
ax.scatter(reference_tobaccos, detected_tp, color="orange", label="TP", zorder=2, marker="x")
ax.scatter(reference_tobaccos, detected_tp_fp, color="midnightblue", label="TP+FP", zorder=2, marker="o")

# 回归线
m_tp_fp, b_tp_fp = np.polyfit(reference_tobaccos, detected_tp_fp, 1)
m_tp, b_tp = np.polyfit(reference_tobaccos, detected_tp, 1)
ax.plot(reference_tobaccos, m_tp_fp * reference_tobaccos + b_tp_fp, color="midnightblue", zorder=1)
ax.plot(reference_tobaccos, m_tp * reference_tobaccos + b_tp, color="orange", zorder=1)

# 坐标轴标签与范围
ax.set_xlabel("Reference tobaccos", fontsize=12)
ax.set_ylabel("Detected tobaccos", fontsize=12)
ax.set_xlim(axis_min, axis_max)
ax.set_ylim(axis_min, axis_max)

# 图例
ax.legend(loc="lower right")

# 注释框
info_text = (
    f"TP+FP: R²={r2_tp_fp:.2f}, RMSE={rmse_tp_fp:.2f}\n"
    f"TP:    R²={r2_tp:.2f}, RMSE={rmse_tp:.2f}\n"
    f"P={mean_precision:.3f}, R={mean_recall:.3f}, F1={mean_f1:.3f}"
)
bbox_props = dict(boxstyle="round,pad=0.4", fc="white", ec="black", lw=0.5)
ax.text(
    0.05,
    0.85,
    info_text,
    transform=ax.transAxes,
    fontsize=11,
    verticalalignment="top",
    bbox=bbox_props,
    fontdict={"font": "Consolas"},
)

# 左上角参数标签框
ax.text(
    0.05,
    0.95,
    "KMeans",
    transform=ax.transAxes,
    fontsize=12,
    verticalalignment="top",
    bbox=dict(boxstyle="round,pad=0.3", fc="lavender", ec="none"),
)

# 布局与展示
plt.tight_layout()
plt.savefig(r"C:\Users\<USER>\Desktop\images\KMeans.pdf")
plt.show()