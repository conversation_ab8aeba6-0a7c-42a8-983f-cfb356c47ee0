_base_ = [
    "../../mmdetection/configs/yolox/yolox_m_8xb8-300e_coco.py",
]

metainfo = dict(
    classes=("object",),
    palette=[
        (
            220,
            20,
            60,
        ),
    ],
)

model = dict(
    bbox_head=dict(num_classes=1),
    test_cfg=dict(score_thr=0.01, nms=dict(type="nms", iou_threshold=0.65), max_per_img=1500),
)

# dataset settings
data_root = r"E:\tobacco\tobacco_det_data\batch2_384\train_data\coco\\"
dataset_type = "CocoDataset"

backend_args = None

train_dataset = dict(
    dataset=dict(
        data_root=data_root,
        ann_file="annotations/train.json",
        data_prefix=dict(img="images/train/"),
        metainfo=metainfo,
    ),
)

train_dataloader = dict(
    batch_size=20,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type="DefaultSampler", shuffle=True),
    dataset=train_dataset,
)

val_dataloader = dict(
    batch_size=6,
    num_workers=4,
    dataset=dict(
        data_root=data_root,
        ann_file="annotations/val.json",
        data_prefix=dict(img="images/val/"),
        metainfo=metainfo,
        test_mode=True,
        backend_args=backend_args,
    ),
)

test_dataloader = val_dataloader

val_evaluator = dict(
    ann_file=data_root + "annotations/val.json",
)
test_evaluator = val_evaluator

# training settings
num_last_epochs = 15

# optimizer
base_lr = 0.0005
optim_wrapper = dict(
    _delete_=True,
    type="AmpOptimWrapper",
    optimizer=dict(type="AdamW", lr=base_lr, betas=(0.9, 0.999), weight_decay=0.05),
)
